import { useUserWithProfile } from '@/hooks/use-auth';
import { supabase } from '@/lib/supabase';
import type { Database } from '@/types/database';
import { useQuery } from '@tanstack/react-query';

// Type definitions
type User = Database['public']['Tables']['users']['Row'];

export interface TeamMember {
  id: string;
  name: string | null;
  email: string | null;
  phone_number: string | null;
  user_role: string | null;
  onboarding_completed: boolean | null;
  created_at: string;
  updated_at: string | null;
}

/**
 * Hook to get all team members for the current user's contractor company
 * Only fetches if the user is a contractor with a company
 */
export function useTeamMembers() {
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useQuery({
    queryKey: ['team-members', contractorId],
    queryFn: async (): Promise<TeamMember[]> => {
      if (!contractorId) throw new Error('Contractor ID is required');

      const { data, error } = await supabase
        .from('users')
        .select(`
          id,
          name,
          email,
          phone_number,
          user_role,
          onboarding_completed,
          created_at,
          updated_at
        `)
        .eq('contractor_id', contractorId)
        .is('deleted_at', null) // Only get non-deleted users
        .order('created_at', { ascending: true }); // Order by join date

      if (error) throw error;
      return (data || []) as TeamMember[];
    },
    enabled: !!contractorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get team member statistics for the current user's contractor company
 */
export function useTeamMemberStats() {
  const { data: user } = useUserWithProfile();
  const contractorId = user?.profile?.contractor_id;

  return useQuery({
    queryKey: ['team-member-stats', contractorId],
    queryFn: async () => {
      if (!contractorId) throw new Error('Contractor ID is required');

      const { data, error } = await supabase
        .from('users')
        .select(`
          user_role,
          onboarding_completed
        `)
        .eq('contractor_id', contractorId)
        .is('deleted_at', null);

      if (error) throw error;

      // Calculate statistics
      const stats = {
        total: data?.length || 0,
        active: data?.filter(u => u.onboarding_completed === true)?.length || 0,
        pending: data?.filter(u => u.onboarding_completed === false)?.length || 0,
        byRole: data?.reduce((acc, user) => {
          const role = user.user_role || 'unknown';
          acc[role] = (acc[role] || 0) + 1;
          return acc;
        }, {} as Record<string, number>) || {},
      };

      return stats;
    },
    enabled: !!contractorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
