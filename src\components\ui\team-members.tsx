'use client';

import { Badge } from '@/components/ui/badge';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { useTeamMembers, useTeamMemberStats } from '@/hooks/use-team-members';
import {
    Calendar,
    CheckCircle,
    Clock,
    Mail,
    Phone,
    UserCheck,
    Users,
} from 'lucide-react';
import { useTranslations } from 'next-intl';

interface TeamMembersProps {
  className?: string;
}

/**
 * Component to display team members for a contractor company
 */
export function TeamMembers({ className }: TeamMembersProps) {
  const {
    data: teamMembers,
    isLoading: membersLoading,
    error: membersError,
  } = useTeamMembers();
  const {
    data: stats,
    isLoading: statsLoading,
    error: statsError,
  } = useTeamMemberStats();

  const t = useTranslations('profilePage.contractorDetails.teamMembers');

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-MY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getRoleBadgeVariant = (role: string | null) => {
    switch (role?.toLowerCase()) {
      case 'contractor':
        return 'default';
      case 'competent_person':
        return 'secondary';
      case 'technician':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const getRoleDisplayName = (role: string | null) => {
    switch (role?.toLowerCase()) {
      case 'contractor':
        return t('roles.contractor');
      case 'competent_person':
        return t('roles.competentPerson');
      case 'technician':
        return t('roles.technician');
      case 'admin':
        return t('roles.admin');
      default:
        return role || t('roles.unknown');
    }
  };

  const getStatusBadge = (onboardingCompleted: boolean | null) => {
    if (onboardingCompleted) {
      return (
        <Badge variant="default" className="flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          {t('status.active')}
        </Badge>
      );
    } else {
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          {t('status.pending')}
        </Badge>
      );
    }
  };

  if (membersLoading || statsLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-blue-600" />
            <span>{t('title')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              <p className="text-sm text-muted-foreground">{t('loading')}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (membersError || statsError) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-blue-600" />
            <span>{t('title')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-destructive">{t('error')}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Users className="h-5 w-5 text-blue-600" />
          <span>{t('title')}</span>
          {stats && (
            <Badge variant="secondary" className="ml-2">
              {stats.total} {t('members')}
            </Badge>
          )}
        </CardTitle>
        <CardDescription>{t('description')}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Statistics Cards */}
        {stats && (
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg">
              <div className="flex items-center space-x-2">
                <UserCheck className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-700 dark:text-green-400">
                  {t('stats.active')}
                </span>
              </div>
              <p className="text-2xl font-bold text-green-800 dark:text-green-300">
                {stats.active}
              </p>
            </div>

            <div className="p-4 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-700 dark:text-yellow-400">
                  {t('stats.pending')}
                </span>
              </div>
              <p className="text-2xl font-bold text-yellow-800 dark:text-yellow-300">
                {stats.pending}
              </p>
            </div>

            <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-700 dark:text-blue-400">
                  {t('stats.total')}
                </span>
              </div>
              <p className="text-2xl font-bold text-blue-800 dark:text-blue-300">
                {stats.total}
              </p>
            </div>
          </div>
        )}

        {/* Team Members Table */}
        {teamMembers && teamMembers.length > 0 ? (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('table.name')}</TableHead>
                  <TableHead>{t('table.contact')}</TableHead>
                  <TableHead>{t('table.role')}</TableHead>
                  <TableHead>{t('table.status')}</TableHead>
                  <TableHead>{t('table.joined')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {teamMembers.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">
                          {member.name || t('table.noName')}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          ID: {member.id.slice(0, 8)}...
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {member.email && (
                          <div className="flex items-center space-x-2 text-sm">
                            <Mail className="h-3 w-3 text-muted-foreground" />
                            <span className="truncate max-w-[200px]">
                              {member.email}
                            </span>
                          </div>
                        )}
                        {member.phone_number && (
                          <div className="flex items-center space-x-2 text-sm">
                            <Phone className="h-3 w-3 text-muted-foreground" />
                            <span>{member.phone_number}</span>
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getRoleBadgeVariant(member.user_role)}>
                        {getRoleDisplayName(member.user_role)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(member.onboarding_completed)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2 text-sm">
                        <Calendar className="h-3 w-3 text-muted-foreground" />
                        <span>{formatDate(member.created_at)}</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-2">{t('noMembers.title')}</p>
            <p className="text-sm text-muted-foreground">
              {t('noMembers.description')}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
